import 'package:evoapp/base/modules/constants.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Network Configuration Constants', () {
    group('nonAuthenticationHttpClientInstance', () {
      test('should have correct string value', () {
        expect(nonAuthenticationHttpClientInstance, 'NonAuthenticationHttpClient');
      });

      test('should be of type String', () {
        expect(nonAuthenticationHttpClientInstance, isA<String>());
      });

      test('should be non-empty string', () {
        expect(nonAuthenticationHttpClientInstance, isNotEmpty);
      });

      test('should be immutable constant', () {
        // This test verifies the constant is declared as const
        // If it wasn't const, this would fail at compile time
        const String testConstant = nonAuthenticationHttpClientInstance;
        expect(testConstant, equals(nonAuthenticationHttpClientInstance));
      });

      test('should follow naming convention for HTTP client instances', () {
        expect(nonAuthenticationHttpClientInstance, contains('HttpClient'));
        expect(nonAuthenticationHttpClientInstance, startsWith('NonAuthentication'));
      });

      test('should be suitable for dependency injection key', () {
        // Verify the constant can be used as a GetIt instance key
        expect(nonAuthenticationHttpClientInstance.length, greaterThan(5));
        expect(nonAuthenticationHttpClientInstance, matches(RegExp(r'^[A-Za-z][A-Za-z0-9]*$')));
      });
    });
  });
}
