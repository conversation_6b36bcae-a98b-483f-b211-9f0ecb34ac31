// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';

void main() {
  group('EvoModuleNames', () {
    group('enum values', () {
      test('returns correct string values', () {
        expect(EvoModuleNames.appState.value, 'app_state_module');
        expect(EvoModuleNames.authBiometric.value, 'auth_biometric_module');
        expect(EvoModuleNames.networkConfig.value, 'network_config_module');
        expect(EvoModuleNames.storageRepository.value, 'storage_repository_module');
        expect(EvoModuleNames.pinReset.value, 'pin_reset_module');
        expect(EvoModuleNames.authenticationSession.value, 'authentication_session_module');
        expect(EvoModuleNames.navigationTheme.value, 'navigation_theme_module');
        expect(EvoModuleNames.appUtilities.value, 'app_utilities_module');
        expect(EvoModuleNames.privilegeValidation.value, 'privilege_validation_module');
        expect(EvoModuleNames.utilityWrapper.value, 'utility_wrapper_module');
      });

      test('contains all expected modules', () {
        expect(EvoModuleNames.values, hasLength(10));
        expect(EvoModuleNames.values, containsAll([
          EvoModuleNames.appState,
          EvoModuleNames.authBiometric,
          EvoModuleNames.networkConfig,
          EvoModuleNames.storageRepository,
          EvoModuleNames.pinReset,
          EvoModuleNames.authenticationSession,
          EvoModuleNames.navigationTheme,
          EvoModuleNames.appUtilities,
          EvoModuleNames.privilegeValidation,
          EvoModuleNames.utilityWrapper,
        ]));
      });

      test('has unique values', () {
        final valueStrings = EvoModuleNames.values.map((e) => e.value).toList();
        final uniqueValues = valueStrings.toSet();
        expect(uniqueValues.length, valueStrings.length);
      });

      test('follows naming convention', () {
        for (final module in EvoModuleNames.values) {
          expect(module.value, endsWith('_module'));
          expect(module.value, matches(RegExp(r'^[a-z_]+$')));
          expect(module.value, isNot(contains(' ')));
        }
      });
    });

    group('static methods', () {
      test('allNames returns all module names', () {
        final allNames = EvoModuleNames.allNames;
        expect(allNames, hasLength(10));
        expect(allNames, contains('app_state_module'));
        expect(allNames, contains('auth_biometric_module'));
        expect(allNames, contains('network_config_module'));
      });

      test('fromValue finds module by string value', () {
        expect(EvoModuleNames.fromValue('app_state_module'), EvoModuleNames.appState);
        expect(EvoModuleNames.fromValue('auth_biometric_module'), EvoModuleNames.authBiometric);
        expect(EvoModuleNames.fromValue('invalid_module'), isNull);
        expect(EvoModuleNames.fromValue(''), isNull);
      });

      test('isValidModuleName validates correctly', () {
        expect(EvoModuleNames.isValidModuleName('app_state_module'), isTrue);
        expect(EvoModuleNames.isValidModuleName('auth_biometric_module'), isTrue);
        expect(EvoModuleNames.isValidModuleName('invalid_module'), isFalse);
        expect(EvoModuleNames.isValidModuleName(''), isFalse);
        expect(EvoModuleNames.isValidModuleName('APP_STATE_MODULE'), isFalse);
      });
    });

    group('module categories', () {
      test('coreModules contains infrastructure modules', () {
        expect(EvoModuleNames.coreModules, hasLength(3));
        expect(EvoModuleNames.coreModules, containsAll([
          EvoModuleNames.appState,
          EvoModuleNames.authBiometric,
          EvoModuleNames.networkConfig,
        ]));
      });

      test('dataModules contains data layer modules', () {
        expect(EvoModuleNames.dataModules, hasLength(1));
        expect(EvoModuleNames.dataModules, contains(EvoModuleNames.storageRepository));
      });

      test('featureModules contains business feature modules', () {
        expect(EvoModuleNames.featureModules, hasLength(2));
        expect(EvoModuleNames.featureModules, containsAll([
          EvoModuleNames.pinReset,
          EvoModuleNames.authenticationSession,
        ]));
      });

      test('uiModules contains UI-related modules', () {
        expect(EvoModuleNames.uiModules, hasLength(1));
        expect(EvoModuleNames.uiModules, contains(EvoModuleNames.navigationTheme));
      });

      test('utilityModules contains utility and helper modules', () {
        expect(EvoModuleNames.utilityModules, hasLength(3));
        expect(EvoModuleNames.utilityModules, containsAll([
          EvoModuleNames.appUtilities,
          EvoModuleNames.privilegeValidation,
          EvoModuleNames.utilityWrapper,
        ]));
      });

      test('all modules are categorized', () {
        final allCategorizedModules = [
          ...EvoModuleNames.coreModules,
          ...EvoModuleNames.dataModules,
          ...EvoModuleNames.featureModules,
          ...EvoModuleNames.uiModules,
          ...EvoModuleNames.utilityModules,
        ];
        expect(allCategorizedModules.length, EvoModuleNames.values.length);
        expect(allCategorizedModules.toSet(), EvoModuleNames.values.toSet());
      });
    });

    group('edge cases', () {
      test('handles null and empty values in fromValue', () {
        expect(EvoModuleNames.fromValue(''), isNull);
        expect(EvoModuleNames.fromValue('   '), isNull);
      });

      test('handles case sensitivity in validation', () {
        expect(EvoModuleNames.isValidModuleName('App_State_Module'), isFalse);
        expect(EvoModuleNames.isValidModuleName('APP_STATE_MODULE'), isFalse);
        expect(EvoModuleNames.isValidModuleName('app_state_module'), isTrue);
      });

      test('validates module name format strictly', () {
        expect(EvoModuleNames.isValidModuleName('app state module'), isFalse);
        expect(EvoModuleNames.isValidModuleName('app-state-module'), isFalse);
        expect(EvoModuleNames.isValidModuleName('appStateModule'), isFalse);
      });
    });
  });
}
